

<template>
    <div class="box-wrap">
            <div class="title-bar">
                <div class="title">{{ title }}</div>
                <slot name="title">
                    <span> </span>
                </slot>
            </div>
            <slot />

        </div>
</template>
<script setup>
import { ref } from "vue";
const props = defineProps({
      title: {
          type: [ String,Number],
      }
  });
</script>
<style scoped lang="less">
    .box-wrap {
        width: 448px;
        height: 564px;
       
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
                    /* 使用渐变作为边框图像 */
                    border-image-source: linear-gradient(180deg, rgba(26, 159, 255, 0) 15%, #1A9FFF 100%);

                    border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
                    border-image-width: 1px;

        .title-bar{
            width: 380px;
            height: 48px;
            padding-left: 52px;
            padding-right: 16px;
            background: url("@/assets/newImages/title-back.svg") no-repeat;
            background-size: cover;
            background-position: center;
            // margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .title {
            font-family: MStiffHei PRC;
font-weight: 400;
font-size: 18px;
line-height: 26px;
background: linear-gradient(0deg, #FFFFFF, #FFFFFF),
linear-gradient(180deg, rgba(26, 159, 255, 0) 20%, rgba(26, 159, 255, 0.45) 80%);

-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
// text-shadow: 0px 0px 4px rgba(26, 159, 255, 0.6);


        }
        .title-name {
            color: #fff;
            font-family: Noto Sans SC;
font-weight: 700;
font-size: 18px;
line-height: 48px;
text-align: center;

        }
    }
</style>
