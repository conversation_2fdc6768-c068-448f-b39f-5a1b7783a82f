<template>
    <div>
        <div class="nav-all-wrapper" v-if="showFlag == 3">
            <!-- 导航按钮 -->
            <div class="nav-all-btns">
                <div
                    :class="active === 1 ? 'active-btn' : 'btn'"
                    @click="setPage(1)"
                >
                    碳排放
                </div>
                <div
                    :class="active === 2 ? 'active-btn' : 'btn'"
                    @click="setPage(2)"
                >
                    用电
                </div>
                <div
                    :class="active === 3 ? 'active-btn' : 'btn'"
                    @click="setPage(3)"
                >
                    水
                </div>
                <div
                    :class="active === 4 ? 'active-btn' : 'btn'"
                    @click="setPage(4)"
                >
                    天然气
                </div>
                <div
                    :class="active === 5 ? 'active-btn' : 'btn'"
                    @click="setPage(5)"
                >
                    蒸汽量
                </div>
            </div>
        </div>
        <energy-consumption-dialog
            :part="part"
            :pickId="energyConsumptionId"
            v-if="energyConsumptionDialogShow"
            @closeDialog="closeDialog"
        ></energy-consumption-dialog>
    </div>
</template>
<!-- <script type="text/javascript" src="./js/CesiumHeatmap"></script> -->
<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
    getCurrentInstance,
    watch,
} from "vue";
import EnergyConsumptionDialog from "./subgroup/EnergyConsumptionDialog.vue";
import {
    energy_map_c,
    energy_map_electricity,
    energy_map_steam,
    energy_map_gas,
    energy_map_water,
} from "../../../assets/js/api/smartEnergy";
import Test from "../ComprehensiveSupervision/subgroup/Test.vue";
// import CesiumHeatmap from "./js/CesiumHeatmap";
// import * as CesiumHeatmap from "CesiumHeatmap";
const { proxy } = getCurrentInstance();
const energyConsumptionDialogShow = ref(false);
const energyConsumptionId = ref(null);
const showFlag = ref(1);
const active = ref(0);
var points = [];
var max = 0;
const test = () => {
    // 设定热力图的四至范围
    let bounds = {
        west: 116.13833844,
        east: 116.13956899,
        south: 37.43582929,
        north: 37.43706916,
    };

    // 初始化热力图
    let heatMap = CesiumHeatmap.create(
        viewer, // your cesium viewer
        bounds, // bounds for heatmap layer
        {
            // heatmap.js options go here
            maxOpacity: 0.75,
        },
    );

    // 设置一些随机的效果，这个可根据实际数据进行开发
    let data = [
        {
            x: 116.1383442264,
            y: 37.4360048372,
            value: 76,
        },
        {
            x: 116.1384363011,
            y: 37.4360298848,
            value: 63,
        },
        {
            x: 116.138368102,
            y: 37.4358360603,
            value: 1,
        },
        {
            x: 116.1385627739,
            y: 37.4358799123,
            value: 21,
        },
        {
            x: 116.1385138501,
            y: 37.4359327669,
            value: 28,
        },
        {
            x: 116.1385031219,
            y: 37.4359730105,
            value: 41,
        },
        {
            x: 116.1384127393,
            y: 37.435928255,
            value: 75,
        },
        {
            x: 116.1384551116,
            y: 37.4359450132,
            value: 3,
        },
        {
            x: 116.1384927196,
            y: 37.4359158649,
            value: 45,
        },
        {
            x: 116.1384938639,
            y: 37.4358498311,
            value: 45,
        },
        {
            x: 116.1385183299,
            y: 37.4360213794,
            value: 93,
        },
        {
            x: 116.1384007925,
            y: 37.4359860133,
            value: 46,
        },
        {
            x: 116.1383604844,
            y: 37.4358298672,
            value: 54,
        },
        {
            x: 116.13851025,
            y: 37.4359098303,
            value: 39,
        },
        {
            x: 116.1383874733,
            y: 37.4358511035,
            value: 34,
        },
        {
            x: 116.1384981796,
            y: 37.4359355403,
            value: 81,
        },
        {
            x: 116.1384504107,
            y: 37.4360332348,
            value: 39,
        },
        {
            x: 116.1385582664,
            y: 37.4359788335,
            value: 20,
        },
        {
            x: 116.1383967364,
            y: 37.4360581999,
            value: 35,
        },
        {
            x: 116.1383839615,
            y: 37.436016316,
            value: 47,
        },
        {
            x: 116.1384082712,
            y: 37.4358423338,
            value: 36,
        },
        {
            x: 116.1385092651,
            y: 37.4358577623,
            value: 69,
        },
        {
            x: 116.138360356,
            y: 37.436046789,
            value: 90,
        },
        {
            x: 116.138471893,
            y: 37.4359184292,
            value: 88,
        },
        {
            x: 116.1385605689,
            y: 37.4360271359,
            value: 81,
        },
        {
            x: 116.1383585714,
            y: 37.4359362476,
            value: 32,
        },
        {
            x: 116.1384939114,
            y: 37.4358844253,
            value: 67,
        },
        {
            x: 116.138466724,
            y: 37.436019121,
            value: 17,
        },
        {
            x: 116.1385504355,
            y: 37.4360614056,
            value: 49,
        },
        {
            x: 116.1383883832,
            y: 37.4358733544,
            value: 82,
        },
        {
            x: 116.1385670669,
            y: 37.4359650236,
            value: 25,
        },
        {
            x: 116.1383416534,
            y: 37.4359310876,
            value: 82,
        },
        {
            x: 116.138525285,
            y: 37.4359394661,
            value: 66,
        },
        {
            x: 116.1385487719,
            y: 37.4360137656,
            value: 73,
        },
        {
            x: 116.1385496029,
            y: 37.4359187277,
            value: 73,
        },
        {
            x: 116.1383989222,
            y: 37.4358556562,
            value: 61,
        },
        {
            x: 116.1385499424,
            y: 37.4359149305,
            value: 67,
        },
        {
            x: 116.138404523,
            y: 37.4359563326,
            value: 90,
        },
        {
            x: 116.1383883675,
            y: 37.4359794855,
            value: 78,
        },
        {
            x: 116.1383967187,
            y: 37.435891185,
            value: 15,
        },
        {
            x: 116.1384610005,
            y: 37.4359044797,
            value: 15,
        },
        {
            x: 116.1384688489,
            y: 37.4360396127,
            value: 91,
        },
        {
            x: 116.1384431875,
            y: 37.4360684409,
            value: 8,
        },
        {
            x: 116.1385411067,
            y: 37.4360645847,
            value: 42,
        },
        {
            x: 116.1385237178,
            y: 37.4358843181,
            value: 31,
        },
        {
            x: 116.1384406464,
            y: 37.4360003831,
            value: 51,
        },
        {
            x: 116.1384679169,
            y: 37.4359950456,
            value: 96,
        },
        {
            x: 116.1384194314,
            y: 37.4358419739,
            value: 22,
        },
        {
            x: 116.1385049792,
            y: 37.4359574813,
            value: 44,
        },
        {
            x: 116.1384097378,
            y: 37.4358598672,
            value: 82,
        },
        {
            x: 116.1384993219,
            y: 37.4360352975,
            value: 84,
        },
        {
            x: 116.1383640499,
            y: 37.4359839518,
            value: 81,
        },
    ];

    //设置最大最小值
    let valueMin = 0;
    let valueMax = 100;
    // 将数据添加到热力图
    heatMap.setWGS84Data(valueMin, valueMax, data);
    //定位到热力图的位置
    viewer.zoomTo(window.viewer.entities);
};
const part = ref(1);
const setPage = (type) => {
    if (active.value == type) {
        active.value = 0;
        window.map1.clearMap();
        if (window.heatmap != null) {
            window.heatmap.setMap(null);
        }
        points = [];
        console.log("----------------");
        energyConsumptionId.value = null;
        initBoundary();
    } else {
        active.value = type;
        part.value = type;
        initBoundary();
        if (type == 1) {
            energy_map_c().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    points = res.data.data.map((x) => {
                        return {
                            id: x.id,
                            lat: x.latitude,
                            lng: x.longitude,
                            count: x.result,
                        };
                    });
                    console.log(points);
                    max = points[0].count;
                }
                if (window.toggle == 3) {
                    initHeatMap();
                } else if (window.toggle == 2) {
                    // test()
                }
            });
        } else if (type == 2) {
            energy_map_electricity().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    points = res.data.data.map((x) => {
                        return {
                            id: x.id,
                            lat: x.latitude,
                            lng: x.longitude,
                            count: x.number,
                        };
                    });
                    console.log(points);
                    max = points[0].count;
                }
                initHeatMap();
            });
        } else if (type == 3) {
            energy_map_water().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    points = res.data.data.map((x) => {
                        return {
                            id: x.id,
                            lat: x.latitude,
                            lng: x.longitude,
                            count: x.number,
                        };
                    });
                    console.log(points);
                    max = points[0].count;
                }
                initHeatMap();
            });
        } else if (type == 4) {
            energy_map_gas().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    points = res.data.data.map((x) => {
                        return {
                            id: x.id,
                            lat: x.latitude,
                            lng: x.longitude,
                            count: x.number,
                        };
                    });
                    console.log(points);
                    max = points[0].count;
                }
                initHeatMap();
            });
        } else {
            energy_map_steam().then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    points = res.data.data.map((x) => {
                        return {
                            id: x.id,
                            lat: x.latitude,
                            lng: x.longitude,
                            count: x.number,
                        };
                    });
                    console.log(points);
                    max = points[0].count;
                }
                initHeatMap();
            });
        }
    }
};
var heatmap;
var circleMarker;
const initHeatMap = () => {
    window.map1.clearMap();
    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
    window.heatmap = new AMap.HeatMap(window.map1, {
        radius: 55, //给定半径
        opacity: [0, 0.8],
    });
    console.log(window.map1);
    window.heatmap.setDataSet({
        data: points,
        max: max,
    });
    for (let i = 0; i < points.length; i++) {
        let center = [];
        center.push(points[i].lng);
        center.push(points[i].lat);
        console.log(center);
        let circleMarker = new AMap.CircleMarker({
            center: center,
            radius: 55, //3D视图下，CircleMarker半径不要超过64px
            strokeColor: "",
            strokeWeight: 2,
            strokeOpacity: 0.5,
            // fillColor:circleColr,
            fillColor: "rgba(18, 48, 77, 0.1)",
            fillOpacity: 0,
            zIndex: 10,
            bubble: true,
            cursor: "pointer",
            clickable: true,
            exData: points[i],
        });
        circleMarker.setMap(window.map1);
        circleMarker.on("click", markerClick);
    }
    // marker.setMap(window.map1);

    window.map1.setFitView();
};

const markerClick = (e) => {
    console.log(e);
    if (e.target._opts.exData != null && e.target._opts.exData !== undefined) {
        console.log(e.target._opts.exData.id);
        energyConsumptionId.value = e.target._opts.exData.id;
        energyConsumptionDialogShow.value = true;
        proxy.$loading.show();
    }
};
var path = [
    [115.107027873843, 37.967476971868],
    [115.108984044575, 37.9676990161805],
    [115.114581935149, 37.9682895127299],
    [115.114875013849, 37.9647435975399],
    [115.120760157275, 37.9654807783805],
    [115.122757109275, 37.9648187005449],
    [115.126154890766, 37.9638912590139],
    [115.127570772583, 37.9589187898429],
    [115.135388661825, 37.9593751355947],
    [115.140649555561, 37.9557639827728],
    [115.140552198161, 37.9511191584419],
    [115.140682599355, 37.9498663851139],
    [115.127644524961, 37.9465171855449],
    [115.127531161592, 37.9469572161843],
    [115.116666868099, 37.9455974855667],
    [115.115731255449, 37.9503398556584],
    [115.110223040793, 37.9505120787165],
    [115.106759744913, 37.9505159984926],
    [115.107027595251, 37.9674533646081],
    [115.107027873843, 37.967476971868]
];
var polyline1 = null;
const initBoundary = () => {
    if (window.map1 != null && window.toggle == 3) {
        console.log("边界");
        polyline1 = new AMap.Polyline({
            path: path,
            strokeColor: "#49D1AF",
            strokeWeight: 3,
            strokeOpacity: 0.9,
            strokeStyle: "dashed",
            zIndex: 50,
            bubble: true,
        });
        console.log(polyline1);
        window.map1.add([polyline1]);
        window.map1.setFitView();
    }
};
onMounted(() => {
    showFlag.value = window.toggle;
    proxy.$bus.on("change_toggle", (val) => {
        console.log("change_toggle ", val);
        showFlag.value = val;
        active.value = 0;
    });

    if (window.heatmap != null) {
        window.heatmap.setMap(null);
    }
    initBoundary();
});
onUnmounted(() => {
    proxy.$bus.off("change_toggle");
});

const closeDialog = (value) => {
    console.log(value);
    if (value == "energyConsumption") {
        energyConsumptionDialogShow.value = false;
    }
    proxy.$loading.hide();
};
</script>

<style lang="less" scoped>
.nav-all-wrapper {
    height: 862px;
    position: absolute;
    z-index: 1000;
    top: 96px;
    left: 496px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    pointer-events: none;

    .nav-all-btns {
        display: flex;
        flex-direction: column;
        gap: 24px;
        pointer-events: auto;
        color: #fff;
        font-family: Noto Sans SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
        text-align: center;

        .btn {
            width: 130px;
            height: 32px;
            background: url("../../../assets/images/jump-btn/self-nav.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }

        .active-btn {
            width: 130px;
            height: 32px;
            background: url("../../../assets/images/jump-btn/self-nav-active.svg")
                no-repeat;
            background-size: cover;
            background-position: center;
        }
    }
}
</style>
