<template>
    <div>
        <div class="closed-management-right">
           <right-top></right-top>
           <right-center  @openDialog="openDialog"></right-center>
        </div>
        <alarm-query-dialog
            v-if="alarmQueryDialogShow"
            @closeDialog="closeDialog"
        ></alarm-query-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineEmits,getCurrentInstance } from "vue";
import WrapSlotTwo from "../../commenNew/WrapSlotTwo.vue";
import RightTop from "./subgroup/RightTop.vue";
import RightCenter from "./subgroup/RightCenter.vue";
import alarmQueryDialog from "./subgroup/alarmQueryDialog.vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);

const openDialog = (val,btnSelect,nowState) => {
    console.log(val,btnSelect,nowState, '打开弹窗');
    emit('openDialog',val,btnSelect,nowState);
}
const alarmQueryDialogShow = ref(false);
const search = () => {
    alarmQueryDialogShow.value = true;
};
const closeDialog = () => {
    alarmQueryDialogShow.value = false;
};
</script>

<style lang="less" scoped>
.closed-management-right {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}
</style>
