<template>
<div class="event-right">
<CardOne title="周边资源">
        <template v-slot>
            <div class="event-right-inner">
                <div class="box-inner">
                    <div class="distance">
                        <div class="title-wrapper">
                            <div class="line_box"></div>
                            <div>距离范围</div>
                        </div>
                        <div class="distance-btns">
                            <div
                                :class="
                                    distanceActive == 200
                                        ? 'active-distance-btn'
                                        : 'distance-btn'
                                "
                                @click="setDistance(200)"
                            >
                                200m
                            </div>
                            <div
                                :class="
                                    distanceActive == 500
                                        ? 'active-distance-btn'
                                        : 'distance-btn'
                                "
                                @click="setDistance(500)"
                            >
                                500m
                            </div>
                            <div
                                :class="
                                    distanceActive == 1000
                                        ? 'active-distance-btn'
                                        : 'distance-btn'
                                "
                                @click="setDistance(1000)"
                            >
                                1km
                            </div>
                            <div
                                :class="
                                    distanceActive == 2000
                                        ? 'active-distance-btn'
                                        : 'distance-btn'
                                "
                                @click="setDistance(2000)"
                            >
                                2km
                            </div>
                        </div>
                    </div>
                    <div class="natural-resources">
                        <div class="title-wrapper">
                            <div class="line_box"></div>
                            <div>资源筛选</div>
                        </div>
                        <div class="checkbox">
                            <el-checkbox-group
                                v-model="checkList"
                                @change="changeCheck"
                            >
                                <!--          <el-checkbox label="微型消防站"></el-checkbox>-->
                                <el-checkbox label="BNCS">避难场所</el-checkbox>
                                <!--          <el-checkbox label="YJGB">应急广播</el-checkbox>-->
                                <el-checkbox label="ZBJK">周边监控</el-checkbox>
                                <el-checkbox label="FHMB">防护目标</el-checkbox>
                                <el-checkbox label="YLJG">医疗机构</el-checkbox>
                                <el-checkbox label="FXYH">风险隐患</el-checkbox>
                                <el-checkbox label="TXBZ">通讯保障</el-checkbox>
                                <!-- <el-checkbox label="扑救力量"></el-checkbox>
                <el-checkbox label="消防人员"></el-checkbox>
                <el-checkbox label="消防单位"></el-checkbox>
                <el-checkbox label="避难场所"></el-checkbox>
                <el-checkbox label="防护目标"></el-checkbox>
                <el-checkbox label="医疗机构"></el-checkbox>
                <el-checkbox label="消防隐患"></el-checkbox> -->
                            </el-checkbox-group>
                        </div>
                        <div class="top_button">
                            <div
                                :class="{
                                    top_button_1: !chooseDispatch,
                                    top_button_2: chooseDispatch,
                                }"
                                @click="chooseTeam"
                            >
                                队伍调度
                            </div>
                            <div
                                :class="{
                                    top_button_1: chooseDispatch,
                                    top_button_2: !chooseDispatch,
                                }"
                                @click="chooseSupplies"
                            >
                                物资调度
                            </div>
                        </div>
                        <div class="materialList">
                            <div
                                class="materialHang"
                                v-for="(item, index) in materialList"
                                :key="index"
                            >
                                {{ item.name }}
                                <div class="materialButton">
                                    <div style="display: flex">
                                        <div
                                            class="material_text"
                                            @click="locationPOI(item)"
                                        >
                                            定位
                                        </div>

                                        <img
                                            src="../../../assets/images/card/fire_3.png"
                                            alt=""
                                            class="material_img"
                                        />
                                    </div>
                                    <div
                                        style="display: flex"
                                        @click="toaddTask(item)"
                                    >
                                        <div class="material_text">调度</div>

                                        <img
                                            src="../../../assets/images/card/fire_4.png"
                                            alt=""
                                            class="material_img"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </CardOne>
</div>
</template>

<script setup>
import {
    getDict,
    emergencyEvent,
    dictionary,
    hxGetCurrentFireWatch,
    overviewList,
    TaskOption,
    supplyOption,
    listOfDepot,
    saveTask,
    recordDetile,
    showPlan,
    showProcess,
    showProcessRight,
    updatePlanStatus,
    updateTaskStatus,
    temporaryList,
    updatetemporaryJob,
    togetplanList,
    getLevelList,
    saveResponse,
    saveRecordEnd,
    analysisdecision,
    getArea,
    reportDetails,
    getMatterMsds,
} from "../../../assets/js/api/fire.js";
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
} from "vue";
import CardOne from "@/components/commenNew/CardOne.vue";
const { proxy } = getCurrentInstance();
const fireEvent = ref(true);
const distanceActive = ref(0);
onMounted(() => {});
proxy.$bus.on("fireEvent", (val) => {
    console.log("fireEvent----------------------", val);
    fireEvent.value = val;
});
const setDistance = (val) => {
    distanceActive.value = val;
    checkList.value = [];
    proxy.$bus.emit("setDistance", val);
};
//资源筛选
const checkList = ref([]);
const changeCheck = (e) => {
    console.log(e);
    proxy.$bus.emit("clickChangeCheck", checkList.value);
};
const materialList = ref([]);
const chooseDispatch = ref(true);
const touetask = ref([]);
const touegoods = ref([]);
const allmarkers = ref([]);
const getoverviewList = () => {
    overviewList({ resourceType: 5013001 }).then((res) => {
        // g给ue传入全量数据，ue给返回数据
        touegoods.value = res.data.data.list;
        // 下拉框数据
        if (!chooseDispatch) {
            materialList.value = res.data.data.list;
        }
    });
    overviewList({ resourceType: 5013002 }).then((res) => {
        touetask.value = res.data.data.list;

        if (chooseDispatch) {
            materialList.value = res.data.data.list;
        }
    });
};
const chooseTeam = () => {
    chooseDispatch.value = true;
    materialList.value = touetask.value;
};
const chooseSupplies = () => {
    chooseDispatch.value = false;
    materialList.value = touegoods.value;
};
const toaddTask = (e) => {
    proxy.$bus.emit("clickToaddTask", e);
};
const locationPOI = (e) => {
    console.log(e);
    if (e.resourceType != undefined) {
        if (e.resourceType == "5013001") {
            proxy.$bus.emit("goodsPOI", e);

            console.log("hahahhahahhahahh是5013001");
            let marker = new AMap.Marker({
                // content: '<div class="two-icon-infrastructure"></div>',
                icon: new AMap.Icon({
                    size: new AMap.Size(44, 44), // 图标尺寸
                    image: new URL(
                        "../../../assets/images/poi/warehouse.png",
                        import.meta.url,
                    ).href, //绝对路径
                    imageSize: new AMap.Size(44, 44),
                }),
                // icon: images,
                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                position: [e.longitude, e.latitude],
                offset: new AMap.Pixel(-22, -44),
            });
            allmarkers.value.push(marker);
            marker.setMap(window.map1);
        } else if (e.resourceType == "5013002") {
            proxy.$bus.emit("rankPOI", e);

            console.log("hahahhahahhahahh是5013002");
            let marker = new AMap.Marker({
                // content: '<div class="two-icon-infrastructure"></div>',
                icon: new AMap.Icon({
                    size: new AMap.Size(44, 44), // 图标尺寸
                    image: new URL(
                        "../../../assets/images/poi/name.png",
                        import.meta.url,
                    ).href, //绝对路径
                    imageSize: new AMap.Size(44, 44),
                }),
                // icon: images,
                // icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                position: [e.longitude, e.latitude],
                offset: new AMap.Pixel(-22, -44),
            });
            allmarkers.value.push(marker);
            marker.setMap(window.map1);
        }
    }
};

onMounted(() => {
    getoverviewList();
    proxy.$bus.on("fireEvent", (val) => {
        console.log("fireEvent----------------------", val);
        allmarkers.value.forEach((r) => {
            window.map1.remove(r);
        });
        allmarkers.value = [];
        distanceActive.value = 0;
        setDistance(0);
        console.log(fireEvent.value);
        //   nextTick(() => {
        //     proxy.$bus.emit('attach',fireEvent.value)

        // })
    });
});
</script>

<style lang="less" scoped>
.event-right {
   width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    right: 24px;
}
.event-right-inner {
   width: 416px;
    height: 928px;
    padding: 16px;
    
    .box-inner {
                width: 400px;
                height: 924px;

                .distance {
                    width: 400px;
                    height: 24px;
                    display: flex;
                    justify-content: space-between;
                    .title-wrapper {
                        display: flex;
                        gap: 4px;
                        font-family: Noto Sans SC;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 24px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #47ebeb;

                        .line_box {
                            width: 2px;
                            height: 8px;
                            background: #47ebeb;
                            margin: auto;
                        }
                    }

                    .distance-btns {
                        width: 306px;
                        height: 24px;
                        display: flex;
                        gap: 8px;
                        font-family: Noto Sans SC;
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 24px;
                        letter-spacing: 0em;
                        text-align: center;
                        color: #ffffff;
                        .active-distance-btn {
                            width: 70.5px;
                            height: 24px;
                            padding: 4px 16px 4px 16px;
                            border-radius: 2;
                            border: 0.5px solid #ffc61a;
                            background: #ffc61a4d;
                        }
                        .distance-btn {
                            width: 70.5px;
                            height: 24px;
                            padding: 4px 16px 4px 16px;
                            border-radius: 2;
                            border: 0.5px solid #1ab2ff;
                            background: #1ab2ff4d;
                        }
                    }
                }
                .natural-resources {
                    width: 400px;
                    height: 216px;
                    margin-top: 24px;
                    .title-wrapper {
                        width: 70px;
                        display: flex;
                        gap: 4px;
                        font-family: Noto Sans SC;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 24px;
                        letter-spacing: 0em;
                        text-align: left;
                        color: #47ebeb;

                        .line_box {
                            width: 2px;
                            height: 8px;
                            background: #47ebeb;
                            margin: auto;
                        }
                    }
                }
                .top_button {
                    /* margin-left: auto; */
                    width: 156px;
                    height: 40px;
                    border-radius: 4px;
                    border: 1px solid #40b5ff;
                    background: rgba(64, 181, 255, 0.3);
                    padding: 4px;
                    display: flex;
                    box-sizing: border-box;
                }
                .top_button_1 {
                    width: 72px;
                    height: 28px;
                    text-align: center;
                    color: #fff;
                    text-align: center;

                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 27px; /* 157.143% */
                    /* padding: 2px 12px; */
                    box-sizing: border-box;
                }
                .top_button_2 {
                    width: 72px;
                    height: 28px;
                    text-align: center;
                    color: #fff;
                    text-align: center;

                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 27px; /* 157.143% */
                    border-radius: 4px;
                    /* padding: 2px 12px; */
                    background: linear-gradient(
                        270deg,
                        #0094ff 0%,
                        #00d1ff 100%
                    );
                    box-sizing: border-box;
                }
                .materialList {
                    height: 560px;
                    margin-top: 30px;
                    color: #fff;
                    font-family: Noto Sans SC;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 500;
                    overflow-y:auto ;
                    &::-webkit-scrollbar {
                        display: none;
                    }
                }
                .materialHang {
                    margin-bottom: 16px;
                    display: flex;
                    text-align: left;
                    /* margin-left: 16px; */
                    padding-left: 16px;
                    width: 390px;
                    height: 42px;
                    border-radius: 4px;
                    line-height: 34px; /* 122.222% */
                    /*background: linear-gradient(*/
                    /*  90deg,*/
                    /*  rgba(10, 209, 236, 0.3) 1.15%,*/
                    /*  rgba(10, 209, 236, 0.4) 53.15%,*/
                    /*  rgba(10, 209, 236, 0) 100%*/
                    /*);*/

                    background: linear-gradient(
                        90deg,
                        rgba(71, 235, 235, 0.3) 1.15%,
                        rgba(71, 235, 235, 0.15) 53.15%,
                        rgba(71, 235, 235, 0) 100%
                    );
                }
                .materialButton {
                    margin-left: auto;
                    display: flex;
                    gap: 24px;
                    color: #47d3ff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                }
                .material_text {
                    line-height: 34px; /* 157.143% */
                }
                .material_img {
                    width: 22px;
                    height: 22px;
                    margin-top: 5px;
                }
                .materialButtonBottom {
                    display: flex;
                    gap: 16px;
                }
                .materialButtonLittle {
                    width: 120px;
                    height: 44px;
                    border-radius: 4px;
                    background: linear-gradient(
                        180deg,
                        #25c3ba 0%,
                        #259fc3 100%
                    );
                    text-align: center;
                    line-height: 40px;
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 18px;
                    font-style: normal;
                    font-weight: 400;
                }
            }
    .checkbox {
        text-align: left;
        width: 400px;
        margin-top: 24px;
    }
    ::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
        color: white;
    }
    ::v-deep .el-checkbox__label {
        color: white;
        width: 100px;
    }
    ::v-deep .el-checkbox__inner {
        background-color: transparent;
        border-color: #0c858c;
    }
    ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #0c858c;
        border-color: #0c858c;
    }
    ::v-deep .el-checkbox {
        margin-bottom: 16px;
    }
}
</style>
