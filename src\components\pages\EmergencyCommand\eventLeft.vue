<template>
    <div class="event-left">
            
                <CardTop title="基础信息">
                    <template v-slot>
                            <div class="box-wrap1-inner">
                            <div class="basic-information" v-if="fireEvent != null">
                                <div class="command_hang1">
                                    <div class="command_hang1_title">事件编号:</div>
                                    <div class="command_hang1_contet">
                                        {{ fireEvent.eventNo || "" }}
                                    </div>
                                </div>
                                <div class="command_hang1">
                                    <div class="command_hang1_title">事件类型:</div>
                                    <div class="command_hang1_contet">
                                        {{ fireEvent.eventTypeName || "" }}
                                    </div>
                                </div>
                                <div class="command_hang1">
                                    <div class="command_hang1_title">告警位置:</div>
                                    <div class="command_hang1_contet">
                                        {{ fireEvent.longitude || "" }},{{
                                            fireEvent.latitude || ""
                                        }}
                                    </div>
                                </div>

                                <div class="command_hang1">
                                    <div class="command_hang1_title">调度进度:</div>
                                    <div class="command_hang1_contet">
                                        {{
                                            dicts.emergency_dispatch_state.find(
                                                (ele) =>
                                                    ele.code == fireEvent.dispatchState,
                                            ) != undefined
                                                ? dicts.emergency_dispatch_state.find(
                                                    (ele) =>
                                                        ele.code ==
                                                        fireEvent.dispatchState,
                                                ).name
                                                : ""
                                        }}
                                    </div>
                                </div>
                                <div class="command_hang1">
                                    <div class="command_hang1_title">告警时间:</div>
                                    <div class="command_hang1_contet">
                                        {{ fireEvent.updateTime || "" }}
                                    </div>
                                </div>
                                <div class="command_hang2">
                                    <div class="command_hang1_title">事件描述:</div>
                                    <div class="command_hang1_contet">
                                        {{ fireEvent.eventDescription || "" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </CardTop>
            <CardBottom title="处置部署">
                    <template v-slot>
            <div class="box-wrap-inner">
                <div class="disposal-deployment">
                    <div class="circular-btns">
                        <div class="circular-btn" @click="clickPlan">
                            <div class="img1"></div>
                            <div class="text">预案响应</div>
                        </div>
                        <div class="circular-btn">
                            <div
                                class="img2"
                                @click.stop="clickResources"
                            ></div>
                            <div class="text">资源调度任务</div>
                        </div>
                        <!-- <div class="circular-btn" @click="clickNotice">
                            <div class="img3"></div>
                            <div class="text">应急广播通知</div>
                        </div> -->
                    </div>
                </div>
                <div class="four-btns">
                    <div class="four-btn" @click.stop="toRecord($event)">
                        执行记录
                    </div>
                    <div class="four-btn" @click="impactFactor">事故模拟</div>
                    <div class="four-btn" @click.stop="toRoom">
                        发起线上会商
                    </div>
                    <div class="four-btn" @click="reportdloag">事故报告</div>
                </div>
            </div>
            </template>
                </CardBottom>
    </div>
</template>

<script setup>
import CardTop from "@/components/commenNew/event/CardTop.vue";
import CardBottom from "@/components/commenNew/event/CardBottom.vue";
import {
    getDict,
    emergencyEvent,
    dictionary,
    hxGetCurrentFireWatch,
    overviewList,
    TaskOption,
    supplyOption,
    listOfDepot,
    saveTask,
    recordDetile,
    showPlan,
    showProcess,
    showProcessRight,
    updatePlanStatus,
    updateTaskStatus,
    temporaryList,
    updatetemporaryJob,
    togetplanList,
    getLevelList,
    saveResponse,
    saveRecordEnd,
    analysisdecision,
    getArea,
    reportDetails,
    getMatterMsds,
} from "../../../assets/js/api/fire.js";
import {
    onMounted,
    reactive,
    ref,
    getCurrentInstance,
    onBeforeUnmount,
    onUnmounted,
    nextTick,
} from "vue";
const { proxy } = getCurrentInstance();
const fireEvent = ref(null);
const dicts = reactive({
    plan_deduction: [],
    response_level: [],
    step_status: [],
    contingent_status: [],
    materiel_type: [],
    task_type: [],
    task_status: [],
    assign_task_type: [],
    event_level: [],
    emergency_dispatch_state: [],
});
const getDictionary = () => {
    for (let item in dicts) {
        console.log(item);
        dictionary({ code: item }).then((res) => {
            console.log(res, "res字典项");
            dicts[item] = res.data.data;
        });
    }

    console.log(dicts);
};
//点击预案响应
const clickPlan = () => {
    console.log(fireEvent.value);
    proxy.$bus.emit("clickPlan", fireEvent.value);
};
//点击资源调度
const clickResources = () => {
    proxy.$bus.emit("clickResources", fireEvent.value);
};
//点击通知
const clickNotice = () => {};
//点击执行记录
const toRecord = (e) => {
    console.log("chufa");
    e.stopPropagation();
    proxy.$bus.emit("clickToRecord", fireEvent.value);
};
//点击事故报告
const reportdloag = () => {
    proxy.$bus.emit("clickReportdloag", fireEvent.value);
};
//点击事故模拟
const impactFactor = () => {
    proxy.$bus.emit("clickImpactFactor", fireEvent.value);
};
//点击线上会商
const toRoom = () => {
    location.href = "https://" + window.location.hostname + ":13004";
};
onMounted(() => {
    getDictionary();
    proxy.$bus.off("fireEvent");
    proxy.$bus.on("fireEvent", (val) => {
        console.log("fireEvent----------------------", val);
        fireEvent.value = val;
        console.log(fireEvent.value);
        //   nextTick(() => {
        //     proxy.$bus.emit('attach',fireEvent.value)

        // })
    });
});
// proxy.$bus.off("fireEvent");
</script>

<style lang="less" scoped>
.event-left {
    width: 448px;
    height: 960px;
    z-index: 1000;
    position: absolute;
    top: 96px;
    left: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
        .box-wrap1-inner {
            width: 416px;
            height: 340px;
            padding:16px;
            .title {
                width: 400px;
                height: 36px;
                background: url("../../../assets/images/title-icon.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
                margin-bottom: 12px;
            }
            .title-name {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 18px;
                font-style: normal;
                font-weight: 500;
                line-height: 26px;
                margin-left: 16px;
            }
            .basic-information {
                .command_hang1 {
                    display: flex;
                    width: 400px;
                    height: 44px;
                    padding: 12px, 0px, 12px, 0px;
                    border: 0px, 0px, 1px, 0px;
                    gap: 10px;
                    border-bottom: 1px solid #47ebeb73;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 44px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: #ffffff;

                    .command_hang1_title {
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 44px;
                        letter-spacing: 0em;
                        text-align: right;
                        color: #47ebeb;
                    }
                }
                .command_hang2 {
                    display: flex;
                    width: 400px;
                    height: 104px;
                    padding: 12px, 0px, 12px, 0px;
                    border: 0px, 0px, 1px, 0px;
                    gap: 10px;
                    border-bottom: 1px solid #47ebeb73;
                    font-family: Noto Sans SC;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 44px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: #ffffff;

                    .command_hang1_title {
                        font-family: Noto Sans SC;
                        font-size: 14px;
                        font-weight: 400;
                        line-height: 44px;
                        letter-spacing: 0em;
                        text-align: right;
                        color: #47ebeb;
                    }
                }
            }
        }
        .box-wrap-inner {
            width: 416px;
            height: 532px;
            padding:16px;
            .title {
                width: 400px;
                height: 36px;
                background: url("../../../assets/images/title-icon.svg")
                    no-repeat;
                background-size: cover;
                background-position: center;
                margin-bottom: 12px;
            }
            .title-name {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 18px;
                font-style: normal;
                font-weight: 500;
                line-height: 26px;
                margin-left: 16px;
            }
            .disposal-deployment {
                .circular-btns {
                    width: 300px;
                    height: 94px;
                    display: flex;
                    justify-content: space-around;
                    margin: auto;
                    margin-top: 12px;

                    .circular-btn {
                        width: 133.33px;
                        height: 94px;
                        padding: 0px, 16px, 0px, 16px;
                        gap: 10px;
                        text-align: center;
                        .text {
                            font-family: Noto Sans SC;
                            font-size: 12px;
                            font-weight: 400;
                            line-height: 18px;
                            letter-spacing: 0em;
                            text-align: center;
                            color: #ffffff;
                        }
                        .img1 {
                            margin: 10px auto;
                            width: 48px;
                            height: 48px;
                            background: url("../../../assets/images/btns/emergency1.svg")
                                no-repeat;
                            background-size: cover;
                            background-position: center;
                        }
                        .img2 {
                            margin: 10px auto;
                            width: 48px;
                            height: 48px;
                            background: url("../../../assets/images/btns/emergency2.svg")
                                no-repeat;
                            background-size: cover;
                            background-position: center;
                        }
                        .img3 {
                            margin: 10px auto;
                            width: 48px;
                            height: 48px;
                            background: url("../../../assets/images/btns/emergency3.svg")
                                no-repeat;
                            background-size: cover;
                            background-position: center;
                        }
                    }
                }
            }
            .four-btns {
                width: 400px;
                height: 410px;
                margin-top: 12px;
                display: flex;
                flex-direction: column;
                gap: 16px;
                .four-btn {
                    width: 400px;
                    height: 48px;
                    padding: 0 16 0 16;
                    gap: 10px;
                    border-radius: 4;
                    background: #30abe826;
                    border: 1px solid #30abe8;
                    font-family: Noto Sans SC;
                    font-size: 16px;
                    font-weight: 400;
                    line-height: 48px;
                    letter-spacing: 0em;
                    text-align: center;
                    color: #ffffff;
                }
            }
        }
}
</style>
