<template>
<div class="dialog-all-wrapper">
        <div class="security-managemen-dialog-wrapper">
            <div class="security-managemen-dialog border">
                <div class="dialog-header">
                    <div class="header-title">产能耗详情</div>
                    <img
                        src="@/assets/newImages/Dialog/dialog-close.svg"
                        class="header-close"
                         @click="closeDialog"
                    />
                </div>
                <div class="dialog-content">
                    
                   <div class="container">
                        <div class="bottom-title">
                            <!-- <div class="icon"></div> -->
                            <div class="text">{{ dialogData.enterprise_name }}</div>
                        </div>
                        <div class="details">
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner special">
                                    <div class="headline">本年累计用电量：</div>
                                    <div class="content">
                                        {{ dialogData.electricity_total }}Kwh
                                    </div>
                                </el-col>
                                <el-col :span="12" class="innner special">
                                    <div class="headline">本年累计用水量:</div>
                                    <div class="content">
                                        {{ dialogData.water_total }}吨
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner">
                                    <div class="headline">本年累计天然气：</div>
                                    <div class="content">
                                        {{ dialogData.gas_total }}千立方米
                                    </div>
                                </el-col>
                                <el-col :span="12" class="innner">
                                    <div class="headline">本年累计蒸汽量：</div>
                                    <div class="content">
                                        {{ dialogData.steam_total }}万
                                    </div>
                                </el-col>
                            </el-row>
                            <el-row :gutter="10">
                                <el-col :span="12" class="innner">
                                    <div class="headline">本年累计碳排放：</div>
                                    <div class="content">{{ dialogData.c_total }}吨</div>
                                </el-col>
                                <el-col :span="12" class="innner">
                                    <div class="headline">本年累计产值：</div>
                                    <div class="content">
                                        {{ dialogData.total_energy }}万元
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                        <div class="chart">
                            <div class="chart-title">
                                <div class="icon"></div>
                                <div class="text">能耗信息</div>
                            </div>
                            <div class="chart-chart">
                                <div class="select-btns">
                                    <div
                                        :class="active === 1 ? 'active-btn' : 'btn'"
                                        @click="changeDoor(1)"
                                    >
                                        电
                                    </div>
                                    <div
                                        :class="active === 2 ? 'active-btn' : 'btn'"
                                        @click="changeDoor(2)"
                                    >
                                        水
                                    </div>
                                    <div
                                        :class="active === 3 ? 'active-btn' : 'btn'"
                                        @click="changeDoor(3)"
                                    >
                                        天然气
                                    </div>
                                    <div
                                        :class="active === 4 ? 'active-btn' : 'btn'"
                                        @click="changeDoor(4)"
                                    >
                                        蒸汽
                                    </div>
                                </div>
                                <div
                                    class="linewrap"
                                    ref="linewrap"
                                    v-show="!showText"
                                ></div>
                                <div v-show="showText" class="no-text">暂无数据</div>
                            </div>
                            <!-- <div ref="img" id="imgCCC" class="img"></div> -->
                        </div>
                    </div>
                </div>
                
            </div>
            <div class="corner-bottom-left"></div>
        </div>
    </div>
    
</template>

<script setup>
import * as echarts from "echarts";
import { setSize } from "../../../../assets/js/echartsSetSize";
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    toRef,
    isRef,
    toRefs,
} from "vue";
import {
    energy_map_info,
    energy_month_map_electricity,
    energy_month_map_steam,
    energy_month_map_gas,
    energy_month_map_water,
} from "../../../../assets/js/api/smartEnergy";

const clickId = ref();
const props = defineProps({
    pickId: {
        type: [Number, String],
    },
    part: {
        type: [Number, String],
    },
});
const { pickId } = toRefs(props);
const { part } = toRefs(props);
const dialogData = ref({
    enterprise_name: "",
    electricity_total: "",
    water_total: "",
    gas_total: "",
    steam_total: "",
    c_total: "",
    total_energy: "",
});
const showText = ref(false);
watch(
    pickId,
    (a, b) => {
        console.log(a, b);
        clickId.value = a;
        if (a != undefined) {
            energy_map_info({ id: a }).then((res) => {
                if (res.data.success && res.data.data && res.data.data.length) {
                    dialogData.value = res.data.data[0];
                }
                console.log(part.value);
                if (part.value != 1) {
                    changeDoor(Number(part.value) - 1);
                }

                electricity();
            });
        }
    },
    {
        immediate: true,
    },
);
const active = ref(1);
const changeDoor = (type) => {
    active.value = type;
    if (type == 1) {
        electricity();
        unit.value = "千瓦时";
    } else if (type == 2) {
        water();
        unit.value = "吨";
    } else if (type == 3) {
        gas();
        unit.value = "千立方米";
    } else {
        steam();
        unit.value = "吨";
    }
};
var mapQuarter = [
        {
            num: '1',
            value:'第一季度'
        },
        {
            num: '2',
            value:'第二季度'
        },
        {
            num: '3',
            value:'第三季度'
        },
        {
            num: '4',
            value:'第四季度'
        }
    ]
const electricity = () => {
    xData = [];
    yData = [0, 0, 0, 0];
    
    energy_month_map_electricity({ id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            showText.value = false;
            var currMonth = new Date().getMonth() + 1;
            var currQuarter = Math.floor(
                currMonth % 3 == 0 ? currMonth / 3 : currMonth / 3 + 1,
            );
            xData = [
                ["第一季度", "第二季度", "第三季度", "第四季度"],
                ["第二季度", "第三季度", "第四季度", "第一季度"],
                ["第三季度", "第四季度", "第一季度", "第二季度"],
                ["第四季度", "第一季度", "第二季度", "第三季度"],
            ][currQuarter - 1];
            res.data.data.forEach((item) => {
                console.log(mapQuarter);
                console.log(mapQuarter.find(x => x.num == item.quarter), '---------------', item.quarter);
                if (mapQuarter.find(x => x.num == item.quarter) != undefined) {
                let middle = mapQuarter.find(x => x.num == item.quarter).value
                    console.log(middle);
                    let index = xData.indexOf(middle);
                    console.log(index);
                    yData[index]=item.total_energy
                }
                // yData[Number(item.quarter) - 1] = Number(item.total_energy);
                // if (item.quarter == '1') {
                //   xData.push('第一季度')
                // } else if (item.quarter == '2') {
                //   xData.push('第二季度')
                // } else if (item.quarter == '3') {
                //   xData.push('第三季度')
                // } else {
                //   xData.push('第四季度')
                // }
                // yData.push(item.total_energy == null ? 0 : Number(item.total_energy))
            });
            console.log(yData);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            showText.value = true;
        }
    });
};
const water = () => {
    xData = [];
    yData = [0, 0, 0, 0];
    energy_month_map_water({ id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            showText.value = false;
            var currMonth = new Date().getMonth() + 1;
            var currQuarter = Math.floor(
                currMonth % 3 == 0 ? currMonth / 3 : currMonth / 3 + 1,
            );
            xData = [
                ["第一季度", "第二季度", "第三季度", "第四季度"],
                ["第二季度", "第三季度", "第四季度", "第一季度"],
                ["第三季度", "第四季度", "第一季度", "第二季度"],
                ["第四季度", "第一季度", "第二季度", "第三季度"],
            ][currQuarter - 1];
            res.data.data.forEach((item) => {
                console.log(mapQuarter);
                console.log(mapQuarter.find(x => x.num == item.quarter), '---------------', item.quarter);
                if (mapQuarter.find(x => x.num == item.quarter) != undefined) {
                let middle = mapQuarter.find(x => x.num == item.quarter).value
                    console.log(middle);
                    let index = xData.indexOf(middle);
                    console.log(index);
                    yData[index]=item.total_energy
                }
                // yData[Number(item.quarter) - 1] = Number(item.total_energy);
                // if (item.quarter == '1') {
                //   xData.push('第一季度')
                // } else if (item.quarter == '2') {
                //   xData.push('第二季度')
                // } else if (item.quarter == '3') {
                //   xData.push('第三季度')
                // } else {
                //   xData.push('第四季度')
                // }
                // yData.push(item.total_energy == null ? 0 : Number(item.total_energy))
            });
            console.log(yData);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            showText.value = true;
        }
    });
};
const steam = () => {
    xData = [];
    yData = [0, 0, 0, 0];
    energy_month_map_steam({ id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            showText.value = false;
            var currMonth = new Date().getMonth() + 1;
            var currQuarter = Math.floor(
                currMonth % 3 == 0 ? currMonth / 3 : currMonth / 3 + 1,
            );
            xData = [
                ["第一季度", "第二季度", "第三季度", "第四季度"],
                ["第二季度", "第三季度", "第四季度", "第一季度"],
                ["第三季度", "第四季度", "第一季度", "第二季度"],
                ["第四季度", "第一季度", "第二季度", "第三季度"],
            ][currQuarter - 1];
            res.data.data.forEach((item) => {
                console.log(mapQuarter);
                console.log(mapQuarter.find(x => x.num == item.quarter), '---------------', item.quarter);
                if (mapQuarter.find(x => x.num == item.quarter) != undefined) {
                let middle = mapQuarter.find(x => x.num == item.quarter).value
                    console.log(middle);
                    let index = xData.indexOf(middle);
                    console.log(index);
                    yData[index]=item.total_energy
                }
                // yData[Number(item.quarter) - 1] = Number(item.total_energy);
                // if (item.quarter == '1') {
                //   xData.push('第一季度')
                // } else if (item.quarter == '2') {
                //   xData.push('第二季度')
                // } else if (item.quarter == '3') {
                //   xData.push('第三季度')
                // } else {
                //   xData.push('第四季度')
                // }
                // yData.push(item.total_energy == null ? 0 : Number(item.total_energy))
            });
            console.log(xData);
            console.log(yData);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            showText.value = true;
        }
    });
};
const gas = () => {
    xData = [];
    yData = [0, 0, 0, 0];
    energy_month_map_gas({ id: clickId.value }).then((res) => {
        if (res.data.success && res.data.data && res.data.data.length) {
            showText.value = false;
            var currMonth = new Date().getMonth() + 1;
            var currQuarter = Math.floor(
                currMonth % 3 == 0 ? currMonth / 3 : currMonth / 3 + 1,
            );
            xData = [
                ["第一季度", "第二季度", "第三季度", "第四季度"],
                ["第二季度", "第三季度", "第四季度", "第一季度"],
                ["第三季度", "第四季度", "第一季度", "第二季度"],
                ["第四季度", "第一季度", "第二季度", "第三季度"],
            ][currQuarter - 1];
            res.data.data.forEach((item) => {
                console.log(mapQuarter);
                console.log(mapQuarter.find(x => x.num == item.quarter), '---------------', item.quarter);
                if (mapQuarter.find(x => x.num == item.quarter) != undefined) {
                let middle = mapQuarter.find(x => x.num == item.quarter).value
                    console.log(middle);
                    let index = xData.indexOf(middle);
                    console.log(index);
                    yData[index]=item.total_energy
                }
                // yData[Number(item.quarter) - 1] = Number(item.total_energy);
                // if (item.quarter == '1') {
                //   xData.push('第一季度')
                // } else if (item.quarter == '2') {
                //   xData.push('第二季度')
                // } else if (item.quarter == '3') {
                //   xData.push('第三季度')
                // } else {
                //   xData.push('第四季度')
                // }
                // yData.push(item.total_energy == null ? 0 : Number(item.total_energy))
            });
            console.log(yData);
            initChart();
            lineChart.setOption(option);
            window.addEventListener("resize", () => {
                lineChart.resize();
            });
        } else {
            showText.value = true;
        }
    });
};
const emit = defineEmits(["closeDialog"]);
const typeActive = ref(1);
const closeDialog = () => {
    console.log("energyConsumption");
    emit("closeDialog", "energyConsumption");
};
const unit = ref("千瓦时");

let option;
let linewrap = ref(null);
let lineChart;
let xData = ref([]);
let yData = ref([]);
const initChart = () => {
    option = {
        grid: {
            left: 15,
            right: 25,
            top: 40,
            bottom: 10,
            containLabel: true,
        },
        tooltip: {
            show: true,
            trigger: "axis",
        },
        legend: {
            show: false,
            x: "right", // 图例水平居中
            y: "top", // 图例垂直居上
            itemStyle: { opacity: 0 }, //去圆点
            textStyle: {
                color: "#fff",
            },
        },
        xAxis: [
            {
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    color: "#1AB2FF",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#397cbc",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        color: "#195384",
                    },
                },
                data: xData,
            },
        ],
        yAxis: [
            {
                type: "value",
                name: `单位：${unit.value}`,
                nameTextStyle: {
                    //y轴上方单位的颜色
                    color: "#fff",
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#FFFFFF",
                    },
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: "#27b4c2",
                    },
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(255, 255, 255, 0.15)",
                    },
                },
            },
        ],
        series: [
            {
                name: "能耗",
                type: "line",
                showSymbol: true,
                symbolSize: 8,
                smooth: true,
                itemStyle: {
                    normal: {
                        color: "#FFC61A",
                        lineStyle: {
                            color: "#FFC61A",
                            width: 1,
                        },
                        areaStyle: {
                            //color: '#94C9EC'
                            color: new echarts.graphic.LinearGradient(
                                0,
                                1,
                                0,
                                0,
                                [
                                    {
                                        offset: 0,
                                        color: "rgba(255, 198, 26, 0.00)",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(255, 198, 26, 0.30)",
                                    },
                                ],
                            ),
                        },
                    },
                },
                data: yData,
            },
        ],
    };
};
onMounted(() => {
    lineChart = echarts.init(linewrap.value);
    xData = ["第一季度", "第二季度", "第三季度", "第四季度"];
    yData = [0, 0, 0, 0];
});
onBeforeUnmount(() => {
    // loopShowTooltip(lineChart, option, { interval: 5000, loopSeries: true }).clearLoop()
    if (lineChart) {
        // setTimeout(() => {
        lineChart.dispose();
        // }, 5000)
    }
});
</script>

<style lang="less" scoped>
.dialog-all-wrapper {
    width: 100vw;
    height: 100vh;
    z-index: 1003;
    // background-color:antiquewhite;
    background-color: rgba(0, 0, 0, 0.5);

    position: absolute;
    top: 0px;
    left: 0px;
    .security-managemen-dialog-wrapper {
        // width: 1160px;
        // height: 720px;
        width: 840px;
    height: 732px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        // 添加底部两个装饰三角形
        &::after,
        .corner-bottom-left {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 6px;
            border-color: rgba(26, 159, 255, 0.6) transparent transparent
                transparent;
            z-index: 1;
        }

        // 左下角装饰三角形
        .corner-bottom-left {
            bottom: -7.5px;
            left: -6px;
            transform: rotate(45deg);
        }

        // 右下角装饰三角形
        &::after {
            bottom: -7.5px;
            right: -7.5px;
            transform: rotate(-45deg);
        }
    }
    .security-managemen-dialog {
        width: 840px;
    height: 732px;

        background-color: rgba(5, 29, 46, 0.6);

        clip-path: polygon(
            10px 0,
            /* 左上角开始 */ calc(100% - 10px) 0,
            /* 右上角左侧 */ 100% 10px,
            /* 右上角下侧 */ 100% calc(100% - 10px),
            /* 右下角上侧 */ calc(100% - 10px) 100%,
            /* 右下角左侧 */ 10px 100%,
            /* 左下角右侧 */ 0 calc(100% - 10px),
            /* 左下角上侧 */ 0 10px /* 左上角下侧 */
        );
    }
    .dialog-header {
        width: 808px;
        height: 56px;
        padding: 0 16px;
        margin-left: -1px;
        background: url("@/assets/newImages/Dialog/dialog-header.svg") no-repeat;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-title {
            font-family: MStiffHei PRC;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            letter-spacing: 0%;
            background: linear-gradient(
                    180deg,
                    rgba(26, 159, 255, 0.45) 20%,
                    rgba(26, 159, 255, 0) 80%
                ),
                linear-gradient(180deg, #ffffff 15.63%, #2ad9ff 87.5%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .header-close {
            width: 16px;
            height: 16px;
        }
    }
    .border {
        border: 1px solid transparent; /* 使用透明的实线边框，使边框本身不可见 */
        /* 使用渐变作为边框图像 */
        border-image-source: linear-gradient(
            360deg,
            rgba(26, 159, 255, 0.6) 0%,
            rgba(26, 159, 255, 0) 100%
        );
        border-image-slice: 1; /* 这决定了如何从渐变图像中“切割”边框 */
        border-image-width: 1px;
        border-top: none;
    }
    // 弹窗内部
    .dialog-content {
        width: 800px;
        height: 636px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap:20px;
        
        
    }
}
.energy-consumption-dialog {
    width: 840px;
    height: 732px;
    border-radius: 4px;
    border: 1px solid rgba(71, 235, 235, 0.15);
    background: rgba(5, 32, 46, 0.6);
    backdrop-filter: blur(2px);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1003;
}



.container {
   
    // background-color: aquamarine;
    .bottom-title {
        width: 792px;
        height: 24px;
        display: flex;
        gap: 4px;
        // margin-top: 16px;
        .icon {
            width: 2px;
            height: 8px;
            background: #47ebeb;
            margin: auto 0;
        }

        .text {
            color: #fff;
            font-family: Noto Sans SC;
            font-size: 22px;
            font-style: normal;
            font-weight: 700;
            line-height: 24px; /* 150% */
        }
    }

    .details {
        margin-top: 12px;
        width: 792px;
        height: 109px;
        // background-color: #47ebeb;
        .innner {
            display: flex;
            margin-top: 8px;

            .headline {
                width: 160px;
                color: #47ebeb;
                text-align: right;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }

            .content {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
        }

        .special {
            margin-top: 0px;
        }
    }

    .chart {
        width: 792px;
        height: 504px;
        flex-shrink: 0;
        //   background: rgba(48, 171, 232, 0.15);
        // margin-top: 17px;
        .chart-title {
            width: 70px;
            height: 24px;
            display: flex;
            gap: 4px;
            // margin-top: 16px;
            .icon {
                width: 2px;
                height: 8px;
                background: #47ebeb;
                margin: auto 0;
            }

            .text {
                color: #fff;
                font-family: Noto Sans SC;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
            }
        }

        .chart-chart {
            width: 792px;
            height: 480px;
            // background-color: #47ebeb;
            .select-btns {
                width: 248px;
                height: 24px;
                display: flex;
                gap: 8px;
                margin-left: 544px;

                .active-btn {
                    width: 56px;
                    height: 24px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 4px;
                    border: 0.5px solid #ffc61a;
                    background: rgba(255, 198, 26, 0.3);
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px; /* 133.333% */
                }

                .btn {
                    width: 56px;
                    height: 24px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 4px;
                    border: 0.622px solid #1ab2ff;
                    background: rgba(26, 178, 255, 0.3);
                    color: #fff;
                    text-align: center;
                    font-family: Noto Sans SC;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 24px; /* 133.333% */
                }
            }

            .linewrap {
                width: 792px;
                height: 440px;
                margin-top: 16px;
            }
            .no-text {
                width: 792px;
                height: 440px;
                margin-top: 16px;
                margin: auto 0;
                color: #fff;
                text-align: center;
                font-family: "Noto Sans SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 440px; /* 150% */
            }
        }
    }
}
</style>
