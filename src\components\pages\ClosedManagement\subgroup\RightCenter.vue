<template>
    <TwoCard title="不安全驾驶行为">
        <template v-slot:title>
            <div class="title-bar">
                <selectVue :sonList="btnOptions" :chooseValue="chooseValue" @choose="selectBtn" />
                <div class="title-bar-right"  @click="openDialog('unsafeDriveDialogShow',nowChoose)">
                    <div>查看详情</div>
                    <img class="right-arrow" src="@/assets/newImages/right-arrow.svg">
                </div>
            </div>
            
        </template>
        <template v-slot>
            <div class="driving-behavior-wrapper">
                <div class="top">
                    <div class="top-item" v-for="(item,index) in topData" :key="index">
                        <div class="top-item-value" :class="index%2==0?'blue-top-item-value':'green-top-item-value'">{{item.total}}<span class="top-item-value-unit">起</span></div>
                        <img class="top-item-img" :src="index%2==0?blueTopImg:greenTopImg">
                        <div class="top-item-name">{{item.alarm_type_name}}</div>
                    </div>
                </div>
                <div class="bottom">
                    <div v-if="noDataFlag" class="list-content">
                        <div class="img-no">暂无数据</div>
                    </div>
                    <div v-else>
                        <autoScroll class="card-container" :step="2" :deltaTime="100" v-if="list&&list.length>3">   
                            <div class="list-content">
                                <div class="list-item" v-for="(item,index) in list" :key="index">
                                </div>
                            </div>
                        </autoScroll>  
                        <div v-else class="list-content">
                            <div class="list-item" v-for="(item,index) in list" :key="index">
                                <table cellspacing="3" cellpadding="0">
                            <tr>
                                <!-- <td>
                    <span class="title"
                        ><el-icon class="icon"><Van /></el-icon>行为：</span
                    >{{ item.behavior }}
                    </td> -->
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Postcard /></el-icon
                                        >车牌：</span
                                    >{{ item.carPlate }}
                                </td>
                                <!-- <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><CircleClose /></el-icon
                                        >违规类型：</span
                                    >{{ item.alarmType }}
                                </td> -->
                            </tr>
                            <tr>
                             
                                
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><CircleClose /></el-icon
                                        >违规类型：</span
                                    >{{ item.alarmType }}
                                </td>
                            </tr>
                            <!-- <tr>
                                <td @click="clickLocation(item)">
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Location /></el-icon
                                        >地点：</span
                                    >{{ item.longitude }}，{{ item.latitude }}
                                </td>
                            </tr> -->
                            <tr>
                                <td>
                                    <span class="title"
                                        ><el-icon class="icon"
                                            ><Clock /></el-icon
                                        >时间：</span
                                    >{{ item.alarmTime }}
                                </td>
                            </tr>
                        </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </TwoCard>
</template>

<script setup>
import {
    ref,
    reactive,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    watch,
    defineEmits,
    nextTick,
    

} from "vue";
import TwoCard from "@/components/commenNew/TwoCard.vue";
import selectVue from "@/components/commenNew/selectVue.vue";
import autoScroll from "@/components/commenNew/autoScroll.vue";
import {
    countAlarm,pageAlarm
} from "@/assets/js/api/closedManagement";
// 导入图片
import blueTopImg from '@/assets/newImages/ClosedManagement/blue-top.svg';
import greenTopImg from '@/assets/newImages/ClosedManagement/green-top.svg';
const { proxy } = getCurrentInstance();
const emit = defineEmits(["openDialog"]);
//打开下钻弹窗
const openDialog = (val,val2) => {
    
    console.log(val,val2, '打开弹窗');
    
    proxy.$emit('openDialog',val,val2);
}

//顶部数据
let topData = ref([
    {
        alarm_type_name:'未按规定路线行驶',
        total:0
    },
    {
        alarm_type_name:'未按规定车道行驶',
        total:0
    },
    {
        alarm_type_name:'未按规定时段行驶',
        total:0
    },
    {
        alarm_type_name:'超速',
        total:0
    },
    {
        alarm_type_name:'违停',
        total:0
    },
    {
        alarm_type_name:'超出边界',
        total:0
    },
])
//数量统计
const getTopData =(val) =>{
    countAlarm({timeType:val}).then(res =>{
        console.log(res);
        if(res.data&&res.data.data&&res.data.data.length>0){
            topData.value = res.data.data;
        }
    })
}
//分页
const getBottomData = (val) =>{
    pageAlarm({timeType:val,pageNum:1,pageSize:10}).then(res =>{
        console.log(res.data.data,'分页------------');
        if(res.data&&res.data.data&&res.data.data.records&&res.data.data.records.length>0){
            list.value = res.data.data.records;
        }
    })
}
//按钮选项
const btnOptions = ref([
    {
        label:'今日',
        value:'d'
    },
    {
        label:'本周',
        value:'w'
    },
    {
        label:'本月',
        value:'m'
    },
    {
        label:'本年',
        value:'y'
    }
])
//按钮
let chooseValue = ref('本年');
//对应的按钮value
let nowChoose = ref('y');
const selectBtn = (val) => {
    console.log(val,'vallllllllll');
    // getData(val.value);
   let key = val.value;
   nowChoose.value = key;
   getTopData(key);
   getBottomData(key);
};

let noDataFlag = ref(false);
let list = ref([]);
onMounted(()=>{
getTopData('y');
getBottomData('y');
})
</script>

<style lang="less" scoped>
.title-bar{
    display: flex;
    gap: 16px;

    .title-bar-right{
        font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
text-align: center;
color: #80EAFF;

        display: flex;
        gap: 4px;
        
        .right-arrow{
            width: 16px;
            height: 16px;
            margin: auto;
        }
    }
}
.driving-behavior-wrapper{
    width: 416px;
height: 552px;
padding: 16px;
display: flex;
flex-direction: column;
gap: 16px;
.top{
    width: 416px;
height: 196px;
display: flex;
gap: 16px;
flex-wrap: wrap;
.top-item{
    width: 128px;
height: 92px;
display: flex;
flex-direction: column;
align-items: center;
.top-item-value{
    font-family: Noto Sans SC;
font-weight: 700;
font-size: 24px;
line-height: 32px;
text-align: center;

}
.blue-top-item-value{
    color: rgba(26, 159, 255, 1);
}
.green-top-item-value{
    color:rgba(23, 207, 191, 1)
}
.top-item-value-unit{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 22px;
text-align: center;
color: rgba(255, 255, 255, 1);

}
.top-item-name{
    font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
color: rgba(255, 255, 255, 1)
}
}
}
.bottom{
    width: 416px;
    height: 340px;
   .card-container{
    width: 416px;
    height: 340px;
    
   }
   .img-no {
            margin: auto 0;
            color: #fff;
            text-align: center;
            font-family: "Noto Sans SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 340px; /* 150% */
        }
   .list-content{
    width: 416px;
    height: 340px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .list-item{
        width: 390px;
height: 76px;
padding: 12px;
border-radius: 4px;
border: 1px solid rgba(25, 159, 255, 1);
font-family: Noto Sans SC;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: rgba(255, 255, 255, 1);
display: flex;
flex-direction: column;
justify-content: space-between;
.title{
    color:rgba(128, 234, 255, 1);
    
}
.icon{
width: 16px;
height: 16px;
margin-right: 8px;

}
    }
   }
}
}
</style>