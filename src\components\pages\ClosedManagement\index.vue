<template>
    <div class="comprehensive-supervision">
        <closed-management-left></closed-management-left>
        <closed-management-right @openDialog="openDialog"></closed-management-right>
        <closed-management-navigation></closed-management-navigation>
        <unsafe-drive-dialog :currentValue="currentValue" v-if="unsafeDriveDialogShow" @closeDialog="closeDialog"></unsafe-drive-dialog>
    </div>
</template>
<script setup>
import ClosedManagementLeft from "./ClosedManagementLeft.vue";
import ClosedManagementRight from "./ClosedManagementRight.vue";
import ClosedManagementNavigation from "./ClosedManagementNavigation.vue";
//不安全驾驶行为弹窗
import unsafeDriveDialog from "./Dialog/unsafeDriveDialog/index.vue";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
let unsafeDriveDialogShow = ref(false);
// 弹窗传参
let currentValue = ref(null);
const openDialog = (dialogName,btnSelect,nowState) => {
    console.log(dialogName,btnSelect,nowState);
    if(dialogName=='unsafeDriveDialogShow'){
        unsafeDriveDialogShow.value = true;

    }
        currentValue.value = btnSelect;
        console.log(currentValue.value,'\\\\\\\\\\');
        

};
const closeDialog = (val) => {
    if(val=='unsafeDriveDialogShow'){
        unsafeDriveDialogShow.value = false;

    }
};
</script>

<style lang="less" scoped></style>
